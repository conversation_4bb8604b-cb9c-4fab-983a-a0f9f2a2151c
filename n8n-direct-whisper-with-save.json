{"name": "Direct Whisper with Text Save", "nodes": [{"parameters": {"filePath": "C:\\Users\\<USER>\\Downloads\\ses.mp3"}, "id": "read-file", "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "requestMethod": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $binary.data }}"}, {"name": "model", "value": "whisper-1"}, {"name": "response_format", "value": "verbose_json"}]}, "options": {"timeout": 300000}}, "id": "whisper-api", "name": "Whisper API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"jsCode": "// Sonucu formatla ve dosya içeriği hazırla\nconst response = $input.first().json;\nconst timestamp = new Date().toISOString();\nconst turkishDate = new Date().toLocaleString('tr-TR');\n\n// Dosya adını oluştur (timestamp ile)\nconst fileName = `transkript_${timestamp.replace(/[:.]/g, '-').replace('T', '_').substring(0, 19)}.txt`;\n\n// Formatlanmış metin içeriği\nconst textContent = `TRANSKRIPT RAPORU\n==================\n\nTarih: ${turkishDate}\nDil: ${response.language || 'Bilinmiyor'}\nSüre: ${response.duration || 'Bilinmiyor'} saniye\n\n--- TRANSKRIPT ---\n${response.text}\n\n--- DETAYLI BİLGİLER ---\n${response.segments ? response.segments.map((seg, i) => `[${seg.start.toFixed(1)}s - ${seg.end.toFixed(1)}s] ${seg.text}`).join('\\n') : 'Segment bilgisi yok'}\n\n--- RAPOR SONU ---\nOluşturulma Zamanı: ${timestamp}\nDosya Adı: ${fileName}`;\n\nreturn {\n  success: true,\n  text: response.text,\n  language: response.language || 'unknown',\n  duration: response.duration || 0,\n  segments: response.segments || [],\n  timestamp: timestamp,\n  fileName: fileName,\n  textContent: textContent\n};"}, "id": "format-result", "name": "Format Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"jsCode": "// Metin içeriğini binary data'ya çevir\nconst data = $input.first().json;\nconst textBuffer = Buffer.from(data.textContent, 'utf8');\n\nreturn {\n  json: data,\n  binary: {\n    data: {\n      data: textBuffer,\n      mimeType: 'text/plain',\n      fileName: data.fileName\n    }\n  }\n};"}, "id": "prepare-binary", "name": "Prepare Binary Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"fileName": "={{ $json.fileName }}", "dataPropertyName": "data"}, "id": "write-file", "name": "Write Text File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"jsCode": "// Final sonuç\nconst result = $input.first().json;\n\nreturn {\n  success: true,\n  message: '<PERSON><PERSON> dosyası başarıyla yazıya çevrildi ve kaydedildi! 🎉',\n  transcription: {\n    text: result.text,\n    language: result.language,\n    duration: result.duration,\n    timestamp: result.timestamp,\n    segmentCount: result.segments ? result.segments.length : 0\n  },\n  file: {\n    name: result.fileName,\n    saved: true,\n    location: 'n8n çalışma dizini',\n    fullPath: `${process.cwd()}/${result.fileName}`\n  },\n  summary: {\n    wordCount: result.text ? result.text.split(' ').length : 0,\n    characterCount: result.text ? result.text.length : 0\n  }\n};"}, "id": "final-result", "name": "Final Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}], "connections": {"Read Binary File": {"main": [[{"node": "Whisper API", "type": "main", "index": 0}]]}, "Whisper API": {"main": [[{"node": "Format Result", "type": "main", "index": 0}]]}, "Format Result": {"main": [[{"node": "Prepare Binary Data", "type": "main", "index": 0}]]}, "Prepare Binary Data": {"main": [[{"node": "Write Text File", "type": "main", "index": 0}]]}, "Write Text File": {"main": [[{"node": "Final Result", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}