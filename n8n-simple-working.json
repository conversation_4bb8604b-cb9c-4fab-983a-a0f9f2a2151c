{"name": "Simple Working Speech to Text", "nodes": [{"parameters": {"filePath": "C:\\Users\\<USER>\\Downloads\\ses.mp3"}, "id": "read-file", "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Binary data'yı kontrol et ve hazırla\nconst binaryData = $input.first().binary.data;\n\nconsole.log('Binary data info:', {\n  hasData: !!binaryData.data,\n  fileName: binaryData.fileName,\n  mimeType: binaryData.mimeType,\n  dataLength: binaryData.data ? binaryData.data.length : 0\n});\n\nreturn {\n  json: {\n    fileName: binaryData.fileName || 'ses.mp3',\n    mimeType: binaryData.mimeType || 'audio/mpeg',\n    fileSize: binaryData.data ? binaryData.data.length : 0,\n    ready: true\n  },\n  binary: {\n    audioFile: {\n      data: binaryData.data,\n      mimeType: binaryData.mimeType || 'audio/mpeg',\n      fileName: binaryData.fileName || 'ses.mp3'\n    }\n  }\n};"}, "id": "prepare-audio", "name": "Prepare Audio", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"requestMethod": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $binary.audioFile }}", "parameterType": "formBinaryData"}, {"name": "model", "value": "whisper-1"}, {"name": "language", "value": "tr"}, {"name": "response_format", "value": "json"}]}, "options": {"timeout": 300000}}, "id": "whisper-request", "name": "Whisper Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// Sonucu formatla\nconst response = $input.first().json;\nconst timestamp = new Date().toISOString();\nconst turkishDate = new Date().toLocaleString('tr-TR');\n\n// Dosya adını oluştur\nconst fileName = `transkript_${timestamp.replace(/[:.]/g, '-').replace('T', '_').substring(0, 19)}.txt`;\n\n// Metin i<PERSON>\nconst textContent = `TRANSKRIPT RAPORU\\n==================\\n\\nTarih: ${turkishDate}\\nDil: ${response.language || 'Bilinmiyor'}\\n\\n--- TRANSKRIPT ---\\n${response.text}\\n\\n--- RAPOR SONU ---\\nOluşturulma Zamanı: ${timestamp}\\nDosya Adı: ${fileName}`;\n\nreturn {\n  success: true,\n  text: response.text,\n  language: response.language || 'unknown',\n  timestamp: timestamp,\n  fileName: fileName,\n  textContent: textContent\n};"}, "id": "format-response", "name": "Format Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Metin dosyasını binary olarak hazırla\nconst data = $input.first().json;\nconst textBuffer = Buffer.from(data.textContent, 'utf8');\n\nreturn {\n  json: data,\n  binary: {\n    textFile: {\n      data: textBuffer,\n      mimeType: 'text/plain; charset=utf-8',\n      fileName: data.fileName\n    }\n  }\n};"}, "id": "prepare-text", "name": "Prepare Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"fileName": "={{ $json.fileName }}", "dataPropertyName": "textFile"}, "id": "save-file", "name": "Save File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Final sonuç\nconst result = $input.first().json;\n\nreturn {\n  success: true,\n  message: '🎉 Başarılı! Ses dosyası yazıya çevrildi ve kaydedildi.',\n  transcription: {\n    text: result.text,\n    language: result.language,\n    timestamp: result.timestamp\n  },\n  file: {\n    name: result.fileName,\n    saved: true,\n    location: 'n8n çalış<PERSON> dizini'\n  },\n  stats: {\n    wordCount: result.text ? result.text.split(' ').length : 0,\n    characterCount: result.text ? result.text.length : 0\n  }\n};"}, "id": "final-output", "name": "Final Output", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}], "connections": {"Read Binary File": {"main": [[{"node": "Prepare Audio", "type": "main", "index": 0}]]}, "Prepare Audio": {"main": [[{"node": "Whisper Request", "type": "main", "index": 0}]]}, "Whisper Request": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}, "Format Response": {"main": [[{"node": "Prepare Text", "type": "main", "index": 0}]]}, "Prepare Text": {"main": [[{"node": "Save File", "type": "main", "index": 0}]]}, "Save File": {"main": [[{"node": "Final Output", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}