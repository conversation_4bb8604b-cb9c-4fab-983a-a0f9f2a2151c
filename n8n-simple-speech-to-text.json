{"name": "Simple Speech to Text", "nodes": [{"parameters": {"httpMethod": "POST", "path": "simple-speech-to-text", "responseMode": "responseNode", "options": {"rawBody": true}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Raw body ile gelen veriyi kontrol et\nconst inputData = $input.first().json;\nconst binary = $input.first().binary;\n\nconsole.log('Input JSON:', JSON.stringify(inputData, null, 2));\nconsole.log('Binary keys:', binary ? Object.keys(binary) : 'No binary');\n\nlet result = {\n  success: true,\n  inputData: inputData,\n  binaryKeys: binary ? Object.keys(binary) : [],\n  hasBinary: !!binary,\n  hasFile: false,\n  fileInfo: null,\n  rawBodyInfo: {\n    hasRawBody: !!inputData.body,\n    bodyType: typeof inputData.body,\n    bodyLength: inputData.body ? inputData.body.length : 0\n  }\n};\n\n// Binary data kontrolü\nif (binary) {\n  for (const key of Object.keys(binary)) {\n    console.log(`Binary key '${key}':`, {\n      fileName: binary[key].fileName,\n      mimeType: binary[key].mimeType,\n      dataLength: binary[key].data ? binary[key].data.length : 0\n    });\n    \n    if (key === 'file' || binary[key].fileName) {\n      result.hasFile = true;\n      result.fileLocation = `binary.${key}`;\n      result.fileData = binary[key];\n      result.fileInfo = {\n        name: binary[key].fileName || key,\n        size: binary[key].data ? binary[key].data.length : 0,\n        type: binary[key].mimeType || 'unknown'\n      };\n      break;\n    }\n  }\n}\n\n// Raw body kontrolü (multipart parsing)\nif (!result.hasFile && inputData.body && typeof inputData.body === 'string') {\n  // Multipart boundary'yi bul\n  const contentType = inputData.headers['content-type'] || '';\n  const boundaryMatch = contentType.match(/boundary=([^;]+)/);\n  \n  if (boundaryMatch) {\n    const boundary = boundaryMatch[1].replace(/\"/g, '');\n    result.boundaryFound = boundary;\n    \n    // Basit multipart parsing\n    const parts = inputData.body.split('--' + boundary);\n    result.partCount = parts.length;\n    \n    for (let i = 0; i < parts.length; i++) {\n      const part = parts[i].trim();\n      if (part && part.includes('Content-Disposition: form-data; name=\"file\"')) {\n        result.hasFile = true;\n        result.fileLocation = `rawBody.part${i}`;\n        \n        // Dosya içeriğini çıkar (basit yaklaşım)\n        const headerEndIndex = part.indexOf('\\r\\n\\r\\n');\n        if (headerEndIndex > -1) {\n          const fileContent = part.substring(headerEndIndex + 4);\n          result.fileData = {\n            data: Buffer.from(fileContent, 'binary'),\n            mimeType: 'audio/mpeg',\n            fileName: 'ses.mp3'\n          };\n          result.fileInfo = {\n            name: 'ses.mp3',\n            size: fileContent.length,\n            type: 'audio/mpeg'\n          };\n        }\n        break;\n      }\n    }\n  }\n}\n\nreturn result;"}, "id": "debug-input", "name": "Debug Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose"}, "conditions": [{"id": "has-file", "leftValue": "={{ $json.hasFile }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}}, "id": "check-file", "name": "Check File", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "requestMethod": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $json.fileData }}"}, {"name": "model", "value": "whisper-1"}, {"name": "response_format", "value": "json"}]}, "options": {"timeout": 300000}}, "id": "whisper-api", "name": "Whisper API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: true, text: $json.text, debug: $('Debug Input').first().json } }}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: false, error: '<PERSON><PERSON><PERSON> bulunamadı', debug: $json, allInputs: $json.allInputs, inputCount: $json.inputCount } }}", "responseCode": 400, "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: false, error: 'API Hatası', details: $json, debug: $('Debug Input').first().json } }}", "responseCode": 500, "options": {}}, "id": "api-error-response", "name": "API Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Debug Input", "type": "main", "index": 0}]]}, "Debug Input": {"main": [[{"node": "Check File", "type": "main", "index": 0}]]}, "Check File": {"main": [[{"node": "Whisper API", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Whisper API": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]], "error": [[{"node": "API Error Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}