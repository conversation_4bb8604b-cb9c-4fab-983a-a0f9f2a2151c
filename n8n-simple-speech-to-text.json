{"name": "Simple Speech to Text", "nodes": [{"parameters": {"httpMethod": "POST", "path": "simple-speech-to-text", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Gelen veriyi kontrol et ve logla\nconsole.log('Gelen veri:', JSON.stringify($input.first().json, null, 2));\n\nconst inputData = $input.first().json;\nlet result = {\n  success: true,\n  receivedData: inputData,\n  hasFile: false,\n  fileInfo: null\n};\n\n// Dosya kontrolü\nif (inputData.body && inputData.body.file) {\n  result.hasFile = true;\n  result.fileInfo = {\n    name: inputData.body.file.filename || inputData.body.file.name || 'unknown',\n    size: inputData.body.file.size || 0,\n    type: inputData.body.file.mimetype || inputData.body.file.type || 'unknown'\n  };\n  result.fileData = inputData.body.file;\n} else if (inputData.file) {\n  result.hasFile = true;\n  result.fileInfo = {\n    name: inputData.file.filename || inputData.file.name || 'unknown',\n    size: inputData.file.size || 0,\n    type: inputData.file.mimetype || inputData.file.type || 'unknown'\n  };\n  result.fileData = inputData.file;\n}\n\nreturn result;"}, "id": "debug-input", "name": "Debug Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose"}, "conditions": [{"id": "has-file", "leftValue": "={{ $json.hasFile }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}}, "id": "check-file", "name": "Check File", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "requestMethod": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $json.fileData }}"}, {"name": "model", "value": "whisper-1"}, {"name": "response_format", "value": "json"}]}, "options": {"timeout": 300000}}, "id": "whisper-api", "name": "Whisper API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: true, text: $json.text, debug: $('Debug Input').first().json } }}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: false, error: '<PERSON><PERSON><PERSON> bulu<PERSON>', debug: $json } }}", "responseCode": 400, "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: false, error: 'API Hatası', details: $json, debug: $('Debug Input').first().json } }}", "responseCode": 500, "options": {}}, "id": "api-error-response", "name": "API Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Debug Input", "type": "main", "index": 0}]]}, "Debug Input": {"main": [[{"node": "Check File", "type": "main", "index": 0}]]}, "Check File": {"main": [[{"node": "Whisper API", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Whisper API": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]], "error": [[{"node": "API Error Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}