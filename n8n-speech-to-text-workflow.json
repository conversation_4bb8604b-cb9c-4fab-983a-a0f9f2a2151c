{"name": "Speech to Text Converter", "nodes": [{"parameters": {"httpMethod": "POST", "path": "speech-to-text", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "speech-to-text-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose"}, "conditions": [{"id": "file-validation", "leftValue": "={{ $json.body.file }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combinator": "and"}}, "id": "file-validation-if", "name": "File Validation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"jsCode": "// Dosya formatı ve boyut kontrolü\nconst inputData = $input.first().json;\nlet fileData;\n\n// Farklı input formatlarını kontrol et\nif (inputData.body && inputData.body.file) {\n  fileData = inputData.body.file;\n} else if (inputData.file) {\n  fileData = inputData.file;\n} else {\n  return {\n    isValid: false,\n    errors: ['Dosya bulunamadı'],\n    fileInfo: {}\n  };\n}\n\n// Dosya bilgilerini al\nconst fileName = fileData.filename || fileData.name || 'unknown';\nconst fileSize = fileData.size || 0;\n\n// Desteklenen formatlar\nconst supportedFormats = ['.mp3', '.wav', '.m4a', '.mp4', '.mpeg', '.mpga', '.webm'];\nconst fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n\n// Maksimum dosya boyutu (25MB - OpenAI Whisper limiti)\nconst maxFileSize = 25 * 1024 * 1024;\n\nlet validationResult = {\n  isValid: true,\n  errors: [],\n  fileInfo: {\n    name: fileName,\n    size: fileSize,\n    extension: fileExtension\n  },\n  fileData: fileData\n};\n\n// Format kontrolü\nif (fileName !== 'unknown' && !supportedFormats.includes(fileExtension)) {\n  validationResult.isValid = false;\n  validationResult.errors.push(`Desteklenmeyen dosya formatı: ${fileExtension}. Desteklenen formatlar: ${supportedFormats.join(', ')}`);\n}\n\n// Boyut kontrolü\nif (fileSize > maxFileSize) {\n  validationResult.isValid = false;\n  validationResult.errors.push(`Dosya boyutu çok büyük: ${(fileSize / 1024 / 1024).toFixed(2)}MB. Maksimum boyut: 25MB`);\n}\n\n// Dosya boş mu kontrolü\nif (fileSize === 0) {\n  validationResult.isValid = false;\n  validationResult.errors.push('Dosya boş veya geçersiz');\n}\n\nreturn validationResult;"}, "id": "file-format-validation", "name": "File Format Validation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-check", "leftValue": "={{ $json.isValid }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}}, "id": "validation-result-if", "name": "Validation Result Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "requestMethod": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $json.fileData }}"}, {"name": "model", "value": "whisper-1"}, {"name": "language", "value": "={{ $json.body?.language || 'auto' }}"}, {"name": "response_format", "value": "verbose_json"}, {"name": "temperature", "value": "0.2"}]}, "options": {"timeout": 300000, "retry": {"enabled": true, "maxRetries": 3, "retryInterval": 5000}}}, "id": "whisper-api-call", "name": "OpenAI Whisper API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 100]}, {"parameters": {"jsCode": "// Whisper API yanıtını işle ve formatla\nconst response = $input.first().json;\n\nlet result = {\n  success: true,\n  timestamp: new Date().toISOString(),\n  transcription: {\n    text: response.text || '',\n    language: response.language || 'unknown',\n    duration: response.duration || 0,\n    segments: response.segments || []\n  },\n  metadata: {\n    model: 'whisper-1',\n    confidence: 'high', // Whisper genellikle yüksek güvenilirlik sağlar\n    processing_time: response.duration ? `${response.duration}s` : 'unknown'\n  }\n};\n\n// Segment bilgilerini temizle (varsa)\nif (result.transcription.segments.length > 0) {\n  result.transcription.segments = result.transcription.segments.map(segment => ({\n    start: segment.start,\n    end: segment.end,\n    text: segment.text\n  }));\n}\n\nreturn result;"}, "id": "format-success-response", "name": "Format Success Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 100]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 100]}, {"parameters": {"jsCode": "// API hata yanıtını işle\nconst error = $input.first().json;\nconst statusCode = $input.first().statusCode || 500;\n\nlet errorResponse = {\n  success: false,\n  timestamp: new Date().toISOString(),\n  error: {\n    code: statusCode,\n    message: 'Se<PERSON> dosyası işlenirken hata oluştu',\n    details: error.error?.message || error.message || 'Bilinmeyen hata'\n  }\n};\n\n// Özel hata mesajları\nif (statusCode === 413) {\n  errorResponse.error.message = 'Dosya boyutu çok büyük (maksimum 25MB)';\n} else if (statusCode === 400) {\n  errorResponse.error.message = 'Geçersiz dosya formatı veya bozuk dosya';\n} else if (statusCode === 401) {\n  errorResponse.error.message = 'API anahtarı geçersiz';\n} else if (statusCode === 429) {\n  errorResponse.error.message = 'API rate limit aşıldı, lütfen daha sonra tekrar deneyin';\n}\n\nreturn errorResponse;"}, "id": "format-api-error", "name": "Format API Error", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "responseCode": 500, "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "id": "api-error-response", "name": "API Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"jsCode": "// Validasyon hatası yanıtını formatla\nconst validationData = $input.first().json;\n\nlet errorResponse = {\n  success: false,\n  timestamp: new Date().toISOString(),\n  error: {\n    code: 400,\n    message: 'Dosya validasyon hatası',\n    details: validationData.errors.join(', '),\n    fileInfo: validationData.fileInfo\n  }\n};\n\nreturn errorResponse;"}, "id": "format-validation-error", "name": "Format Validation Error", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "responseCode": 400, "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "id": "validation-error-response", "name": "Validation Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 400]}, {"parameters": {"jsCode": "// Dosya eksik hatası\nlet errorResponse = {\n  success: false,\n  timestamp: new Date().toISOString(),\n  error: {\n    code: 400,\n    message: '<PERSON><PERSON><PERSON> bulunamadı',\n    details: 'Lütfen geçerli bir ses dosyası yü<PERSON> (MP3, WAV, M4A formatlarında)'\n  }\n};\n\nreturn errorResponse;"}, "id": "format-missing-file-error", "name": "Format Missing File Error", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "responseCode": 400, "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "id": "missing-file-error-response", "name": "Missing File Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 500]}], "connections": {"Webhook Trigger": {"main": [[{"node": "File Validation", "type": "main", "index": 0}]]}, "File Validation": {"main": [[{"node": "File Format Validation", "type": "main", "index": 0}], [{"node": "Format Missing File Error", "type": "main", "index": 0}]]}, "File Format Validation": {"main": [[{"node": "Validation Result Check", "type": "main", "index": 0}]]}, "Validation Result Check": {"main": [[{"node": "OpenAI Whisper API", "type": "main", "index": 0}], [{"node": "Format Validation Error", "type": "main", "index": 0}]]}, "OpenAI Whisper API": {"main": [[{"node": "Format Success Response", "type": "main", "index": 0}]], "error": [[{"node": "Format API Error", "type": "main", "index": 0}]]}, "Format Success Response": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Format API Error": {"main": [[{"node": "API Error Response", "type": "main", "index": 0}]]}, "Format Validation Error": {"main": [[{"node": "Validation Error Response", "type": "main", "index": 0}]]}, "Format Missing File Error": {"main": [[{"node": "Missing File Error Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "speech-to-text", "name": "Speech to Text"}], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}