# n8n Speech-to-Text Test Örnekleri

## cURL Test Komutları

### Temel Ses Dosyası Yükleme
```bash
curl -X POST http://localhost:5678/webhook/speech-to-text \
  -F "file=@example.mp3" \
  -H "Content-Type: multipart/form-data"
```

### Dil Belirtme ile Test
```bash
curl -X POST http://localhost:5678/webhook/speech-to-text \
  -F "file=@turkish_audio.wav" \
  -F "language=tr" \
  -H "Content-Type: multipart/form-data"
```

### Farklı Format Testleri
```bash
# MP3 Test
curl -X POST http://localhost:5678/webhook/speech-to-text \
  -F "file=@audio.mp3" \
  -v

# WAV Test  
curl -X POST http://localhost:5678/webhook/speech-to-text \
  -F "file=@audio.wav" \
  -v

# M4A Test
curl -X POST http://localhost:5678/webhook/speech-to-text \
  -F "file=@audio.m4a" \
  -v
```

## Postman Collection JSON

```json
{
  "info": {
    "name": "n8n Speech-to-Text API",
    "description": "Test collection for speech-to-text workflow",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Upload Audio File - Success",
      "request": {
        "method": "POST",
        "header": [],
        "body": {
          "mode": "formdata",
          "formdata": [
            {
              "key": "file",
              "type": "file",
              "src": "example.mp3"
            },
            {
              "key": "language",
              "value": "tr",
              "type": "text"
            }
          ]
        },
        "url": {
          "raw": "{{base_url}}/webhook/speech-to-text",
          "host": ["{{base_url}}"],
          "path": ["webhook", "speech-to-text"]
        }
      },
      "response": []
    },
    {
      "name": "Upload Audio File - No Language",
      "request": {
        "method": "POST",
        "header": [],
        "body": {
          "mode": "formdata",
          "formdata": [
            {
              "key": "file",
              "type": "file",
              "src": "example.wav"
            }
          ]
        },
        "url": {
          "raw": "{{base_url}}/webhook/speech-to-text",
          "host": ["{{base_url}}"],
          "path": ["webhook", "speech-to-text"]
        }
      },
      "response": []
    },
    {
      "name": "Upload Invalid Format - Error Test",
      "request": {
        "method": "POST",
        "header": [],
        "body": {
          "mode": "formdata",
          "formdata": [
            {
              "key": "file",
              "type": "file",
              "src": "document.pdf"
            }
          ]
        },
        "url": {
          "raw": "{{base_url}}/webhook/speech-to-text",
          "host": ["{{base_url}}"],
          "path": ["webhook", "speech-to-text"]
        }
      },
      "response": []
    },
    {
      "name": "No File Upload - Error Test",
      "request": {
        "method": "POST",
        "header": [],
        "body": {
          "mode": "formdata",
          "formdata": []
        },
        "url": {
          "raw": "{{base_url}}/webhook/speech-to-text",
          "host": ["{{base_url}}"],
          "path": ["webhook", "speech-to-text"]
        }
      },
      "response": []
    }
  ],
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:5678",
      "type": "string"
    }
  ]
}
```

## JavaScript Test Kodu

```javascript
// Node.js ile test
const FormData = require('form-data');
const fs = require('fs');
const axios = require('axios');

async function testSpeechToText(filePath, language = null) {
  try {
    const form = new FormData();
    form.append('file', fs.createReadStream(filePath));
    
    if (language) {
      form.append('language', language);
    }

    const response = await axios.post(
      'http://localhost:5678/webhook/speech-to-text',
      form,
      {
        headers: {
          ...form.getHeaders(),
        },
        timeout: 300000, // 5 dakika timeout
      }
    );

    console.log('Success:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    return null;
  }
}

// Test çalıştırma
testSpeechToText('./test-audio.mp3', 'tr');
```

## Python Test Kodu

```python
import requests
import json

def test_speech_to_text(file_path, language=None):
    url = 'http://localhost:5678/webhook/speech-to-text'
    
    files = {'file': open(file_path, 'rb')}
    data = {}
    
    if language:
        data['language'] = language
    
    try:
        response = requests.post(
            url, 
            files=files, 
            data=data,
            timeout=300  # 5 dakika timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            print("Success:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return result
        else:
            error = response.json()
            print("Error:")
            print(json.dumps(error, indent=2, ensure_ascii=False))
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None
    finally:
        files['file'].close()

# Test çalıştırma
test_speech_to_text('./test-audio.wav', 'tr')
```

## Beklenen Yanıt Örnekleri

### Başarılı Yanıt
```json
{
  "success": true,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "transcription": {
    "text": "Merhaba, bu bir test ses dosyasıdır. Türkçe konuşma tanıma sistemini test ediyoruz.",
    "language": "tr",
    "duration": 8.5,
    "segments": [
      {
        "start": 0.0,
        "end": 3.2,
        "text": "Merhaba, bu bir test ses dosyasıdır."
      },
      {
        "start": 3.2,
        "end": 8.5,
        "text": "Türkçe konuşma tanıma sistemini test ediyoruz."
      }
    ]
  },
  "metadata": {
    "model": "whisper-1",
    "confidence": "high",
    "processing_time": "8.5s"
  }
}
```

### Validasyon Hatası
```json
{
  "success": false,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "error": {
    "code": 400,
    "message": "Dosya validasyon hatası",
    "details": "Desteklenmeyen dosya formatı: .pdf. Desteklenen formatlar: .mp3, .wav, .m4a, .mp4, .mpeg, .mpga, .webm",
    "fileInfo": {
      "name": "document.pdf",
      "size": 1024000,
      "extension": ".pdf"
    }
  }
}
```

### Dosya Boyutu Hatası
```json
{
  "success": false,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "error": {
    "code": 400,
    "message": "Dosya validasyon hatası",
    "details": "Dosya boyutu çok büyük: 30.5MB. Maksimum boyut: 25MB",
    "fileInfo": {
      "name": "large_audio.mp3",
      "size": 31457280,
      "extension": ".mp3"
    }
  }
}
```

## Test Senaryoları

1. **Pozitif Testler**:
   - Geçerli MP3 dosyası yükleme
   - Geçerli WAV dosyası yükleme
   - Dil parametresi ile test
   - Dil parametresi olmadan test

2. **Negatif Testler**:
   - Geçersiz dosya formatı (.txt, .pdf)
   - Çok büyük dosya (>25MB)
   - Boş dosya
   - Dosya olmadan istek
   - Bozuk ses dosyası

3. **Performans Testleri**:
   - Uzun ses dosyaları (20+ dakika)
   - Eş zamanlı çoklu istek
   - Ağ kesintisi simülasyonu
