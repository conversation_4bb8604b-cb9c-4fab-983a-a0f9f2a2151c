# n8n Speech-to-Text Workflow Dokümantasyonu

## Genel Bakış
Bu n8n workflow'u, ses dosyalarını metne dönüşt<PERSON>ren kapsamlı bir otomasyon sistemidir. OpenAI Whisper API kullanarak yüksek kaliteli transkripsiyon sağlar.

## Özellikler
- ✅ Çoklu format desteği (MP3, WAV, M4A, MP4, MPEG, MPGA, WEBM)
- ✅ Dosya boyutu ve format validasyonu
- ✅ OpenAI Whisper API entegrasyonu
- ✅ Kapsamlı hata yönetimi
- ✅ Detaylı yanıt formatları
- ✅ Retry mekanizması
- ✅ Dil desteği (otomatik algılama)

## Workflow Adımları

### 1. Webhook Trigger
- **Node**: `n8n-nodes-base.webhook`
- **Endpoint**: `/speech-to-text`
- **Method**: POST
- **Amaç**: <PERSON>s dosyas<PERSON> yüklemelerini kabul eder

### 2. File Validation
- **Node**: `n8n-nodes-base.if`
- **Amaç**: <PERSON><PERSON>n istekte dosya olup olmadığını kontrol eder
- **Koşul**: `$json.body.file` var mı?

### 3. File Format Validation
- **Node**: `n8n-nodes-base.code`
- **Amaç**: Dosya formatı ve boyut kontrolü
- **Kontroller**:
  - Desteklenen formatlar: `.mp3`, `.wav`, `.m4a`, `.mp4`, `.mpeg`, `.mpga`, `.webm`
  - Maksimum boyut: 25MB (OpenAI Whisper limiti)
  - Dosya boş mu kontrolü

### 4. Validation Result Check
- **Node**: `n8n-nodes-base.if`
- **Amaç**: Validasyon sonucuna göre yönlendirme
- **Koşul**: `isValid === true`

### 5. OpenAI Whisper API
- **Node**: `n8n-nodes-base.httpRequest`
- **URL**: `https://api.openai.com/v1/audio/transcriptions`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Parametreler**:
  - `file`: Ses dosyası
  - `model`: whisper-1
  - `language`: Otomatik algılama veya belirtilen dil
  - `response_format`: verbose_json
  - `temperature`: 0.2 (daha tutarlı sonuçlar için)
- **Timeout**: 5 dakika
- **Retry**: 3 deneme, 5 saniye aralık

### 6. Response Formatting
- **Success Response**: Başarılı transkripsiyon sonuçlarını formatlar
- **API Error Response**: API hatalarını formatlar
- **Validation Error Response**: Validasyon hatalarını formatlar
- **Missing File Error Response**: Dosya eksik hatalarını formatlar

## API Kullanımı

### İstek Formatı
```bash
curl -X POST http://your-n8n-instance/webhook/speech-to-text \
  -F "file=@audio.mp3" \
  -F "language=tr"  # Opsiyonel
```

### Başarılı Yanıt
```json
{
  "success": true,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "transcription": {
    "text": "Transkript edilen metin burada görünür",
    "language": "tr",
    "duration": 45.2,
    "segments": [
      {
        "start": 0.0,
        "end": 5.5,
        "text": "İlk segment metni"
      }
    ]
  },
  "metadata": {
    "model": "whisper-1",
    "confidence": "high",
    "processing_time": "45.2s"
  }
}
```

### Hata Yanıtları

#### Dosya Validasyon Hatası
```json
{
  "success": false,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "error": {
    "code": 400,
    "message": "Dosya validasyon hatası",
    "details": "Desteklenmeyen dosya formatı: .txt",
    "fileInfo": {
      "name": "test.txt",
      "size": 1024,
      "extension": ".txt"
    }
  }
}
```

#### API Hatası
```json
{
  "success": false,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "error": {
    "code": 413,
    "message": "Dosya boyutu çok büyük (maksimum 25MB)",
    "details": "Request entity too large"
  }
}
```

## Kurulum Adımları

### 1. n8n Kurulumu
```bash
npm install -g n8n
# veya
npx n8n
```

### 2. OpenAI API Anahtarı Ayarlama
1. n8n arayüzünde **Credentials** bölümüne gidin
2. **OpenAI** credential'ı oluşturun
3. API anahtarınızı girin

### 3. Workflow İmport Etme
1. n8n arayüzünde **Import from JSON** seçeneğini kullanın
2. `n8n-speech-to-text-workflow.json` dosyasını yükleyin
3. Credential'ları bağlayın

### 4. Webhook URL'ini Alma
1. Webhook Trigger node'una tıklayın
2. **Test URL** veya **Production URL**'yi kopyalayın

## Güvenlik Önerileri

1. **API Anahtarı Güvenliği**: OpenAI API anahtarınızı güvenli tutun
2. **Rate Limiting**: Yüksek trafikli kullanım için rate limiting ekleyin
3. **File Size Limits**: Dosya boyutu limitlerini ihtiyacınıza göre ayarlayın
4. **Authentication**: Webhook'a authentication ekleyebilirsiniz

## Performans Optimizasyonu

1. **Timeout Ayarları**: Uzun ses dosyaları için timeout'u artırın
2. **Retry Logic**: Ağ sorunları için retry sayısını ayarlayın
3. **Caching**: Sık kullanılan dosyalar için cache mekanizması ekleyebilirsiniz

## Desteklenen Diller
OpenAI Whisper 99+ dili destekler:
- Türkçe (tr)
- İngilizce (en)
- Almanca (de)
- Fransızca (fr)
- İspanyolca (es)
- ve daha fazlası...

## Sorun Giderme

### Yaygın Hatalar
1. **401 Unauthorized**: API anahtarını kontrol edin
2. **413 Request Entity Too Large**: Dosya boyutunu küçültün
3. **Timeout**: Timeout süresini artırın
4. **Unsupported format**: Desteklenen formatları kullanın

### Log İnceleme
n8n execution history'den detaylı log'ları inceleyebilirsiniz.

## Genişletme Önerileri

1. **Database Integration**: Transkriptleri veritabanında saklayın
2. **Email Notifications**: Tamamlanan işlemler için email bildirimi
3. **File Storage**: Ses dosyalarını cloud storage'da saklayın
4. **Batch Processing**: Çoklu dosya işleme desteği
5. **Translation**: Transkript sonrası çeviri ekleme
