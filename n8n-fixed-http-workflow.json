{"name": "Fixed HTTP Speech to Text with Save", "nodes": [{"parameters": {"filePath": "C:\\Users\\<USER>\\Downloads\\ses.mp3"}, "id": "read-file", "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Binary data'yı doğru formata çevir\nconst binaryData = $input.first().binary.data;\n\nreturn {\n  json: {\n    fileName: binaryData.fileName || 'ses.mp3',\n    mimeType: binaryData.mimeType || 'audio/mpeg',\n    fileSize: binaryData.data ? binaryData.data.length : 0\n  },\n  binary: {\n    file: {\n      data: binaryData.data,\n      mimeType: binaryData.mimeType || 'audio/mpeg',\n      fileName: binaryData.fileName || 'ses.mp3'\n    }\n  }\n};"}, "id": "prepare-file", "name": "Prepare File", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "requestMethod": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $binary.file }}", "parameterType": "formBinaryData"}, {"name": "model", "value": "whisper-1"}, {"name": "language", "value": "tr"}, {"name": "response_format", "value": "verbose_json"}, {"name": "temperature", "value": "0.2"}]}, "options": {"timeout": 300000, "retry": {"enabled": true, "maxRetries": 3, "retryInterval": 5000}}}, "id": "whisper-api", "name": "Whisper API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// Transkript sonucunu formatla ve dosya hazırla\nconst response = $input.first().json;\nconst timestamp = new Date().toISOString();\nconst turkishDate = new Date().toLocaleString('tr-TR');\n\n// Dosya adını oluştur\nconst fileName = `fixed_transkript_${timestamp.replace(/[:.]/g, '-').replace('T', '_').substring(0, 19)}.txt`;\n\n// Formatlanmış metin içeriği\nconst textContent = `FIXED HTTP TRANSKRIPT RAPORU\n=============================\n\nTarih: ${turkishDate}\nDil: ${response.language || 'Bilinmiyor'}\nSüre: ${response.duration || 'Bilinmiyor'} saniye\n\n--- TRANSKRIPT ---\n${response.text}\n\n--- DETAYLI BİLGİLER ---\n${response.segments ? response.segments.map((seg, i) => `[${seg.start.toFixed(1)}s - ${seg.end.toFixed(1)}s] ${seg.text}`).join('\\n') : 'Segment bilgisi yok'}\n\n--- İSTATİSTİKLER ---\nKelime Sayısı: ${response.text ? response.text.split(' ').length : 0}\nKarakter Sayısı: ${response.text ? response.text.length : 0}\nSegment Sayısı: ${response.segments ? response.segments.length : 0}\n\n--- RAPOR SONU ---\nOluşturulma Zamanı: ${timestamp}\nDosya Adı: ${fileName}\nKaynak: OpenAI Whisper API (Fixed HTTP)`;\n\nreturn {\n  success: true,\n  text: response.text,\n  language: response.language || 'unknown',\n  duration: response.duration || 0,\n  segments: response.segments || [],\n  timestamp: timestamp,\n  fileName: fileName,\n  textContent: textContent,\n  stats: {\n    wordCount: response.text ? response.text.split(' ').length : 0,\n    characterCount: response.text ? response.text.length : 0,\n    segmentCount: response.segments ? response.segments.length : 0\n  }\n};"}, "id": "format-result", "name": "Format Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Metin içeriğini binary data'ya çevir\nconst data = $input.first().json;\nconst textBuffer = Buffer.from(data.textContent, 'utf8');\n\nreturn {\n  json: data,\n  binary: {\n    data: {\n      data: textBuffer,\n      mimeType: 'text/plain',\n      fileName: data.fileName\n    }\n  }\n};"}, "id": "prepare-text-binary", "name": "Prepare Text Binary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"fileName": "={{ $json.fileName }}", "dataPropertyName": "data"}, "id": "write-file", "name": "Write Text File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Final sonuç ve özet\nconst result = $input.first().json;\n\nreturn {\n  success: true,\n  message: '🎉 Ses dosyası başarıyla yazıya çevrildi ve kaydedildi! (Fixed HTTP)',\n  transcription: {\n    text: result.text,\n    language: result.language,\n    duration: result.duration,\n    timestamp: result.timestamp\n  },\n  file: {\n    name: result.fileName,\n    saved: true,\n    location: 'n8n çalışma dizini',\n    fullPath: `${process.cwd()}/${result.fileName}`\n  },\n  statistics: result.stats,\n  preview: {\n    firstWords: result.text ? result.text.substring(0, 100) + '...' : '',\n    totalSegments: result.segments ? result.segments.length : 0\n  }\n};"}, "id": "final-result", "name": "Final Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}], "connections": {"Read Binary File": {"main": [[{"node": "Prepare File", "type": "main", "index": 0}]]}, "Prepare File": {"main": [[{"node": "Whisper API", "type": "main", "index": 0}]]}, "Whisper API": {"main": [[{"node": "Format Result", "type": "main", "index": 0}]]}, "Format Result": {"main": [[{"node": "Write Text File", "type": "main", "index": 0}]]}, "Write Text File": {"main": [[{"node": "Final Result", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}