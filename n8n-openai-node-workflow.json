{"name": "OpenAI Node Speech to Text with Save", "nodes": [{"parameters": {"filePath": "C:\\Users\\<USER>\\Downloads\\ses.mp3"}, "id": "read-file", "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"resource": "audio", "operation": "transcribe", "binaryPropertyName": "data", "options": {"language": "tr", "responseFormat": "verbose_json"}}, "id": "openai-transcribe", "name": "OpenAI Transcribe", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [460, 300], "credentials": {"openAiApi": {"id": "openai-credential", "name": "OpenAI API"}}}, {"parameters": {"jsCode": "// Transkript sonucunu formatla ve dosya hazırla\nconst response = $input.first().json;\nconst timestamp = new Date().toISOString();\nconst turkishDate = new Date().toLocaleString('tr-TR');\n\n// Dosya adını oluştur\nconst fileName = `openai_transkript_${timestamp.replace(/[:.]/g, '-').replace('T', '_').substring(0, 19)}.txt`;\n\n// Formatlanmış metin içeriği\nconst textContent = `OPENAI TRANSKRIPT RAPORU\n========================\n\nTarih: ${turkishDate}\nDil: ${response.language || 'Bilinmiyor'}\nSüre: ${response.duration || 'Bilinmiyor'} saniye\n\n--- TRANSKRIPT ---\n${response.text}\n\n--- DETAYLI BİLGİLER ---\n${response.segments ? response.segments.map((seg, i) => `[${seg.start.toFixed(1)}s - ${seg.end.toFixed(1)}s] ${seg.text}`).join('\\n') : 'Segment bilgisi yok'}\n\n--- İSTATİSTİKLER ---\nKelime Sayısı: ${response.text ? response.text.split(' ').length : 0}\nKarakter Sayısı: ${response.text ? response.text.length : 0}\nSegment Sayısı: ${response.segments ? response.segments.length : 0}\n\n--- RAPOR SONU ---\nOluşturulma Zamanı: ${timestamp}\nDosya Adı: ${fileName}\nKaynak: OpenAI Whisper API (n8n Node)`;\n\nreturn {\n  success: true,\n  text: response.text,\n  language: response.language || 'unknown',\n  duration: response.duration || 0,\n  segments: response.segments || [],\n  timestamp: timestamp,\n  fileName: fileName,\n  textContent: textContent,\n  stats: {\n    wordCount: response.text ? response.text.split(' ').length : 0,\n    characterCount: response.text ? response.text.length : 0,\n    segmentCount: response.segments ? response.segments.length : 0\n  }\n};"}, "id": "format-result", "name": "Format Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"fileName": "={{ $json.fileName }}", "dataPropertyName": "textContent"}, "id": "write-file", "name": "Write Text File", "type": "n8n-nodes-base.writeTextFile", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"jsCode": "// Final sonuç ve özet\nconst result = $input.first().json;\n\nreturn {\n  success: true,\n  message: '🎉 Ses dosyası başarıyla yazıya çevrildi ve kaydedildi!',\n  transcription: {\n    text: result.text,\n    language: result.language,\n    duration: result.duration,\n    timestamp: result.timestamp\n  },\n  file: {\n    name: result.fileName,\n    saved: true,\n    location: 'n8n çalışma dizini',\n    fullPath: `${process.cwd()}/${result.fileName}`\n  },\n  statistics: result.stats,\n  preview: {\n    firstWords: result.text ? result.text.substring(0, 100) + '...' : '',\n    totalSegments: result.segments ? result.segments.length : 0\n  }\n};"}, "id": "final-result", "name": "Final Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}], "connections": {"Read Binary File": {"main": [[{"node": "OpenAI Transcribe", "type": "main", "index": 0}]]}, "OpenAI Transcribe": {"main": [[{"node": "Format Result", "type": "main", "index": 0}]]}, "Format Result": {"main": [[{"node": "Write Text File", "type": "main", "index": 0}]]}, "Write Text File": {"main": [[{"node": "Final Result", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}