{"name": "Direct Whisper Test", "nodes": [{"parameters": {"filePath": "C:\\Users\\<USER>\\Downloads\\ses.mp3"}, "id": "read-file", "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "requestMethod": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $binary.data }}"}, {"name": "model", "value": "whisper-1"}, {"name": "response_format", "value": "json"}]}, "options": {"timeout": 300000}}, "id": "whisper-api", "name": "Whisper API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"jsCode": "// Sonucu formatla\nconst response = $input.first().json;\n\nreturn {\n  success: true,\n  text: response.text,\n  language: response.language || 'unknown',\n  timestamp: new Date().toISOString()\n};"}, "id": "format-result", "name": "Format Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}], "connections": {"Read Binary File": {"main": [[{"node": "Whisper API", "type": "main", "index": 0}]]}, "Whisper API": {"main": [[{"node": "Format Result", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}