<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Dosyası Yazıya Çevirme</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        input[type="file"] {
            display: none;
        }
        .file-label {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .file-label:hover {
            background-color: #0056b3;
        }
        .language-select {
            margin: 20px 0;
        }
        select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .upload-btn {
            background-color: #28a745;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        .upload-btn:hover {
            background-color: #218838;
        }
        .upload-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            text-align: center;
            color: #007bff;
        }
        .transcript {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
        .file-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Ses Dosyası Yazıya Çevirme</h1>
        
        <form id="uploadForm">
            <div class="upload-area" id="uploadArea">
                <label for="audioFile" class="file-label">
                    📁 Ses Dosyası Seçin
                </label>
                <input type="file" id="audioFile" accept=".mp3,.wav,.m4a,.mp4,.mpeg,.mpga,.webm">
                <div class="file-info" id="fileInfo"></div>
                <p style="margin-top: 15px; color: #666;">
                    Desteklenen formatlar: MP3, WAV, M4A, MP4, MPEG, MPGA, WEBM<br>
                    Maksimum dosya boyutu: 25MB
                </p>
            </div>

            <div class="language-select">
                <label for="language">🌍 Dil (Opsiyonel):</label>
                <select id="language">
                    <option value="">Otomatik Algıla</option>
                    <option value="tr">Türkçe</option>
                    <option value="en">İngilizce</option>
                    <option value="de">Almanca</option>
                    <option value="fr">Fransızca</option>
                    <option value="es">İspanyolca</option>
                    <option value="it">İtalyanca</option>
                    <option value="pt">Portekizce</option>
                    <option value="ru">Rusça</option>
                    <option value="ja">Japonca</option>
                    <option value="ko">Korece</option>
                    <option value="zh">Çince</option>
                    <option value="ar">Arapça</option>
                </select>
            </div>

            <button type="submit" class="upload-btn" id="uploadBtn" disabled>
                🚀 Yazıya Çevir
            </button>
        </form>

        <div id="result" class="result">
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const audioFile = document.getElementById('audioFile');
        const fileInfo = document.getElementById('fileInfo');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadForm = document.getElementById('uploadForm');
        const result = document.getElementById('result');
        const resultContent = document.getElementById('resultContent');
        const language = document.getElementById('language');

        // n8n webhook URL'ini buraya yazın - n8n'den aldığınız gerçek URL'i yazın
        // Örnek: 'http://localhost:5678/webhook-test/12345678-1234-1234-1234-123456789012'
        const WEBHOOK_URL = 'http://localhost:5678/webhook-test/simple-speech-to-text';

        // Drag & Drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                audioFile.files = files;
                handleFileSelect();
            }
        });

        // Dosya seçimi
        audioFile.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = audioFile.files[0];
            if (file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                fileInfo.innerHTML = `
                    <strong>Seçilen dosya:</strong> ${file.name}<br>
                    <strong>Boyut:</strong> ${fileSize} MB<br>
                    <strong>Tip:</strong> ${file.type}
                `;
                uploadBtn.disabled = false;
            } else {
                fileInfo.innerHTML = '';
                uploadBtn.disabled = true;
            }
        }

        // Form gönderimi
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const file = audioFile.files[0];
            if (!file) {
                showResult('Lütfen bir dosya seçin!', 'error');
                return;
            }

            // Loading durumu
            uploadBtn.disabled = true;
            uploadBtn.textContent = '⏳ İşleniyor...';
            showResult('Ses dosyası işleniyor, lütfen bekleyin...', 'loading');

            try {
                const formData = new FormData();
                formData.append('file', file);
                
                if (language.value) {
                    formData.append('language', language.value);
                }

                const response = await fetch(WEBHOOK_URL, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showResult(`
                        <h3>✅ Başarılı!</h3>
                        <p><strong>Dil:</strong> ${data.transcription.language}</p>
                        <p><strong>Süre:</strong> ${data.transcription.duration} saniye</p>
                        <div class="transcript">
                            <strong>Transkript:</strong><br>
                            ${data.transcription.text}
                        </div>
                    `, 'success');
                } else {
                    showResult(`
                        <h3>❌ Hata!</h3>
                        <p><strong>Hata Kodu:</strong> ${data.error.code}</p>
                        <p><strong>Mesaj:</strong> ${data.error.message}</p>
                        <p><strong>Detay:</strong> ${data.error.details}</p>
                    `, 'error');
                }
            } catch (error) {
                showResult(`
                    <h3>❌ Bağlantı Hatası!</h3>
                    <p>n8n sunucusuna bağlanılamadı. Lütfen kontrol edin:</p>
                    <ul>
                        <li>n8n çalışıyor mu? (http://localhost:5678)</li>
                        <li>Workflow aktif mi?</li>
                        <li>Webhook URL'i doğru mu?</li>
                    </ul>
                    <p><strong>Hata:</strong> ${error.message}</p>
                `, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '🚀 Yazıya Çevir';
            }
        });

        function showResult(content, type) {
            result.className = `result ${type}`;
            result.style.display = 'block';
            resultContent.innerHTML = content;
        }
    </script>
</body>
</html>
