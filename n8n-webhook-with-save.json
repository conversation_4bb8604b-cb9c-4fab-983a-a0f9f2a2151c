{"name": "Webhook Speech to Text with Save", "nodes": [{"parameters": {"httpMethod": "POST", "path": "speech-to-text-save", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Gelen dosyayı kontrol et\nconst inputData = $input.first().json;\nconst binary = $input.first().binary;\n\nlet result = {\n  hasFile: false,\n  fileData: null,\n  error: null\n};\n\n// Binary data'da dosya ara\nif (binary && Object.keys(binary).length > 0) {\n  const fileKey = Object.keys(binary).find(key => \n    binary[key].fileName || \n    key === 'file' || \n    binary[key].mimeType?.startsWith('audio/')\n  );\n  \n  if (fileKey) {\n    result.hasFile = true;\n    result.fileData = binary[fileKey];\n    result.fileInfo = {\n      name: binary[fileKey].fileName || 'audio.mp3',\n      size: binary[fileKey].data ? binary[fileKey].data.length : 0,\n      type: binary[fileKey].mimeType || 'audio/mpeg'\n    };\n  }\n}\n\n// Body'de dosya ara\nif (!result.hasFile && inputData.body) {\n  if (inputData.body.file) {\n    result.hasFile = true;\n    result.fileData = inputData.body.file;\n    result.fileInfo = {\n      name: inputData.body.file.filename || 'audio.mp3',\n      size: inputData.body.file.size || 0,\n      type: inputData.body.file.mimetype || 'audio/mpeg'\n    };\n  }\n}\n\nif (!result.hasFile) {\n  result.error = 'Dosya bulunamadı';\n}\n\nreturn result;"}, "id": "check-file", "name": "Check File", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose"}, "conditions": [{"id": "has-file", "leftValue": "={{ $json.hasFile }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}}, "id": "file-validation", "name": "File Validation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "requestMethod": "POST", "url": "https://api.openai.com/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.openAiApi.apiKey }}"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "={{ $json.fileData }}"}, {"name": "model", "value": "whisper-1"}, {"name": "response_format", "value": "verbose_json"}]}, "options": {"timeout": 300000}}, "id": "whisper-api", "name": "Whisper API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"jsCode": "// Transkript sonucunu formatla ve dosya hazırla\nconst response = $input.first().json;\nconst timestamp = new Date().toISOString();\nconst turkishDate = new Date().toLocaleString('tr-TR');\n\n// Dosya adını oluştur\nconst fileName = `webhook_transkript_${timestamp.replace(/[:.]/g, '-').replace('T', '_').substring(0, 19)}.txt`;\n\n// Formatlanmış metin içeriği\nconst textContent = `WEBHOOK TRANSKRIPT RAPORU\n==========================\n\nTarih: ${turkishDate}\nDil: ${response.language || 'Bilinmiyor'}\nSüre: ${response.duration || 'Bilinmiyor'} saniye\n\n--- TRANSKRIPT ---\n${response.text}\n\n--- DETAYLI BİLGİLER ---\n${response.segments ? response.segments.map((seg, i) => `[${seg.start.toFixed(1)}s - ${seg.end.toFixed(1)}s] ${seg.text}`).join('\\n') : 'Segment bilgisi yok'}\n\n--- RAPOR SONU ---\nOluşturulma Zamanı: ${timestamp}\nDosya Adı: ${fileName}\nKaynak: Webhook API`;\n\nreturn {\n  success: true,\n  text: response.text,\n  language: response.language || 'unknown',\n  duration: response.duration || 0,\n  segments: response.segments || [],\n  timestamp: timestamp,\n  fileName: fileName,\n  textContent: textContent\n};"}, "id": "format-response", "name": "Format Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"jsCode": "// Metin içeriğini binary data'ya çevir\nconst data = $input.first().json;\nconst textBuffer = Buffer.from(data.textContent, 'utf8');\n\nreturn {\n  json: data,\n  binary: {\n    data: {\n      data: textBuffer,\n      mimeType: 'text/plain',\n      fileName: data.fileName\n    }\n  }\n};"}, "id": "prepare-text-binary", "name": "Prepare Text Binary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"fileName": "={{ $json.fileName }}", "dataPropertyName": "data"}, "id": "save-text-file", "name": "Save Text File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: true, message: 'Se<PERSON> dosyası başarıyla yazıya çevrildi ve kaydedildi! 🎉', transcription: { text: $json.text, language: $json.language, duration: $json.duration, timestamp: $json.timestamp }, file: { name: $json.fileName, saved: true, location: 'n8n çalış<PERSON> dizini' }, summary: { wordCount: $json.text ? $json.text.split(' ').length : 0, characterCount: $json.text ? $json.text.length : 0 } } }}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: false, error: '<PERSON><PERSON><PERSON> bulu<PERSON>', message: '<PERSON><PERSON>t<PERSON> geçerli bir ses dosyası yü<PERSON> (MP3, WAV, M4A formatlarında)', debug: $json } }}", "responseCode": 400, "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: false, error: 'API Hatası', message: 'OpenAI Whisper API ile iletişim kurulamadı', details: $json } }}", "responseCode": 500, "options": {}}, "id": "api-error-response", "name": "API Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Check File", "type": "main", "index": 0}]]}, "Check File": {"main": [[{"node": "File Validation", "type": "main", "index": 0}]]}, "File Validation": {"main": [[{"node": "Whisper API", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Whisper API": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]], "error": [[{"node": "API Error Response", "type": "main", "index": 0}]]}, "Format Response": {"main": [[{"node": "Prepare Text Binary", "type": "main", "index": 0}]]}, "Prepare Text Binary": {"main": [[{"node": "Save Text File", "type": "main", "index": 0}]]}, "Save Text File": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}